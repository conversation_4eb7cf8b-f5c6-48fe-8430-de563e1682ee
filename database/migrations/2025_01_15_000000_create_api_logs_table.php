<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_logs', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['traces_outgoing', 'our_incoming']);
            $table->string('endpoint')->nullable();
            $table->string('method', 10)->nullable();
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->integer('status_code')->nullable();
            $table->string('status_message')->nullable();
            $table->decimal('duration_ms', 10, 2)->nullable();
            $table->string('user_agent')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->date('date_range_start')->nullable();
            $table->date('date_range_end')->nullable();
            $table->integer('certificates_count')->default(0);
            $table->text('error_message')->nullable();
            $table->boolean('success')->default(false);
            $table->timestamps();
            
            $table->index(['type', 'created_at']);
            $table->index(['success', 'created_at']);
            $table->index('date_range_start');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_logs');
    }
}; 
# TRACES API Authentication Troubleshooting Guide

## Problem
TracesNtChedClient works locally but fails with "Unauthenticated Exception" on the server.

## Quick Fix Steps

### 1. Add Environment Variables
Add these to your server's `.env` file:

```env
# TRACES API Configuration
TRACES_USERNAME=n00385tm
TRACES_AUTH_KEY=7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh
TRACES_CLIENT_ID=onispa-mr
TRACES_USE_PRODUCTION=true
TRACES_TIMEOUT=60
TRACES_VERIFY_SSL=true
```

### 2. Test the Connection
Run this command on your server:

```bash
php artisan traces:test
```

For detailed debugging:
```bash
php artisan traces:test --debug
```

### 3. Check Server Requirements

**PHP Extensions Required:**
- curl
- openssl
- xml
- dom

**Check with:**
```bash
php -m | grep -E "(curl|openssl|xml|dom)"
```

### 4. Common Server Issues & Solutions

#### Issue: SSL/TLS Problems
**Symptoms:** CURL SSL errors, certificate verification failures

**Solutions:**
```env
# Try disabling SSL verification temporarily
TRACES_VERIFY_SSL=false
```

Or update your server's CA certificates:
```bash
# Ubuntu/Debian
sudo apt-get update && sudo apt-get install ca-certificates

# CentOS/RHEL
sudo yum update ca-certificates
```

#### Issue: Firewall Blocking Outbound Connections
**Symptoms:** Connection timeouts, network unreachable

**Solutions:**
- Allow outbound HTTPS (port 443) to:
  - `webgate.ec.europa.eu` (production)
  - `webgate.training.ec.europa.eu` (training)
- Test connectivity:
```bash
curl -I https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2
```

#### Issue: Timezone Differences
**Symptoms:** Authentication works sometimes but not others

**Solutions:**
```bash
# Set server timezone to UTC
sudo timedatectl set-timezone UTC

# Or in PHP
date_default_timezone_set('UTC');
```

#### Issue: Memory/Timeout Limits
**Symptoms:** Script timeouts, memory exhaustion

**Solutions:**
```env
# Increase timeout
TRACES_TIMEOUT=120
```

In `php.ini`:
```ini
max_execution_time = 300
memory_limit = 512M
```

### 5. Advanced Debugging

#### Check Laravel Logs
```bash
tail -f storage/logs/laravel.log
```

#### Test Different Endpoints
The client can test multiple endpoints. Check which one works:

```bash
php artisan traces:test --debug
```

#### Manual CURL Test
```bash
# Test basic connectivity
curl -v -X POST \
  -H "Content-Type: text/xml; charset=utf-8" \
  -H "SOAPAction: findChedCertificate" \
  --data-raw '<test>data</test>' \
  https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2
```

### 6. Environment-Specific Configurations

#### For Shared Hosting
```env
TRACES_VERIFY_SSL=false
TRACES_TIMEOUT=30
```

#### For Docker/Container Environments
```env
TRACES_VERIFY_SSL=true
TRACES_TIMEOUT=60
```

#### For High-Security Servers
```env
TRACES_VERIFY_SSL=true
TRACES_TIMEOUT=120
```

### 7. Monitoring & Logging

The updated client now logs detailed information:

- **Request details:** endpoint, size, timeout
- **Response details:** HTTP code, size, timing
- **Errors:** CURL errors, HTTP errors, authentication failures

Check `storage/logs/laravel.log` for these entries.

### 8. If Nothing Works

1. **Contact your hosting provider** about:
   - Outbound HTTPS restrictions
   - SSL/TLS configuration
   - PHP extension availability

2. **Try the training endpoint first:**
   ```env
   TRACES_USE_PRODUCTION=false
   ```

3. **Use the debug command** and share the output:
   ```bash
   php artisan traces:test --debug > traces_debug.log 2>&1
   ```

## Changes Made

1. **Environment Configuration:** Moved hardcoded credentials to config/services.php
2. **Better Error Handling:** Enhanced CURL options and error reporting
3. **Server Compatibility:** Added SSL/TLS options and timeout controls
4. **Debugging Tools:** Created artisan command for testing
5. **Logging:** Added comprehensive logging for troubleshooting

## Testing Checklist

- [ ] Environment variables are set correctly
- [ ] `php artisan traces:test` passes
- [ ] Server can reach TRACES endpoints
- [ ] SSL certificates are valid
- [ ] PHP extensions are installed
- [ ] Firewall allows outbound HTTPS
- [ ] Timezone is set to UTC
- [ ] Laravel logs show successful requests

<?php

namespace App\Providers;

class TracesNtChedClient {
    private $username;
    private $authKey;
    private $clientId;
    private $useProduction;

    public function __construct($username, $authKey, $clientId, $useProduction = false) {
        $this->username = $username;
        $this->authKey = trim($authKey);
        $this->clientId = $clientId;
        $this->useProduction = $useProduction;

        // Ensure UTC timezone for consistent timestamp generation
        date_default_timezone_set('UTC');
    }

  

   
    public function getValidatedFishCertificates($startDate, $endDate) {
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
            : 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2';

        // Log current server time for debugging
        \Log::info('TRACES API Request Time Debug', [
            'server_timezone' => date_default_timezone_get(),
            'server_time' => date('Y-m-d H:i:s T'),
            'utc_time' => gmdate('Y-m-d H:i:s T'),
            'timestamp' => time(),
            'start_date' => $startDate,
            'end_date' => $endDate
        ]);

        $soapRequest = $this->createSoapRequest(
            '<ched:FindChedCertificateRequest>
                <ched:Type>P</ched:Type>
                <ched:Status>70</ched:Status>
                <ched:UpdateDateTimeRange>
                    <base:From>'.$startDate.'T00:00:00Z</base:From>
                    <base:To>'.$endDate.'T23:59:59Z</base:To>
                </ched:UpdateDateTimeRange>
            </ched:FindChedCertificateRequest>'
        );

        try {
            $response = $this->sendSoapRequest($endpoint, $soapRequest);
            return $this->parseResponse($response);
        } catch (\Exception $e) {
            // If we get an authentication error, try different timestamp methods
            if (strpos($e->getMessage(), 'SOAP Fault') !== false ||
                strpos($e->getMessage(), 'Unauthenticated') !== false) {

                \Log::warning('TRACES API authentication failed, trying alternative methods', [
                    'error' => $e->getMessage()
                ]);

                return $this->tryAlternativeAuthMethods($startDate, $endDate, $endpoint);
            }

            throw $e;
        }
    }

    private function tryAlternativeAuthMethods($startDate, $endDate, $endpoint) {
        $methods = [
            'method_1' => 'Standard timestamp format',
            'method_2' => 'Alternative timestamp format',
            'method_3' => 'Rounded timestamp format',
            'method_4' => 'Manual UTC adjustment'
        ];

        foreach ($methods as $method => $description) {
            \Log::info("Trying TRACES authentication: {$description}");

            # Run these on your host server (outside Docker)

# Check NTP sync

# Network from host
            try {
                $soapRequest = $this->createSoapRequestWithMethod(
                    '<ched:FindChedCertificateRequest>
                        <ched:Type>P</ched:Type>
                        <ched:Status>70</ched:Status>
                        <ched:UpdateDateTimeRange>
                            <base:From>'.$startDate.'T00:00:00Z</base:From>
                            <base:To>'.$endDate.'T23:59:59Z</base:To>
                        </ched:UpdateDateTimeRange>
                    </ched:FindChedCertificateRequest>',
                    $method
                );

                $response = $this->sendSoapRequest($endpoint, $soapRequest);

                if (strpos($response, 'SOAP Fault') === false &&
                    strpos($response, 'Unauthenticated') === false) {

                    \Log::info("TRACES authentication successful with: {$description}");
                    return $this->parseResponse($response);
                }

            } catch (\Exception $e) {
                \Log::warning("Method {$method} failed: " . $e->getMessage());
                continue;
            }
        }

        throw new \Exception('All authentication methods failed. Please check server time synchronization and credentials.');
    }

    private function createSoapRequestWithMethod($body, $method = 'method_1') {
        // Ensure UTC timezone
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        switch ($method) {
            case 'method_1':
                // Standard method
                $now = new \DateTime('now', new \DateTimeZone('UTC'));
                $created = $now->format('Y-m-d\TH:i:s\Z');
                $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                break;

            case 'method_2':
                // Alternative with milliseconds
                $now = new \DateTime('now', new \DateTimeZone('UTC'));
                $created = $now->format('Y-m-d\TH:i:s.v\Z');
                $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s.v\Z');
                break;

            case 'method_3':
                // Rounded to nearest second
                $now = new \DateTime('now', new \DateTimeZone('UTC'));
                $now->setTime($now->format('H'), $now->format('i'), $now->format('s'), 0);
                $created = $now->format('Y-m-d\TH:i:s\Z');
                $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                break;

            case 'method_4':
                // Manual UTC adjustment
                $timestamp = time();
                $now = new \DateTime('@' . $timestamp, new \DateTimeZone('UTC'));
                $created = $now->format('Y-m-d\TH:i:s\Z');
                $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                break;

            default:
                $now = new \DateTime('now', new \DateTimeZone('UTC'));
                $created = $now->format('Y-m-d\TH:i:s\Z');
                $expires = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
        }

        $passwordDigest = base64_encode(
            sha1($nonceRaw . $created . $this->authKey, true)
        );

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>' . $body . '</soapenv:Body>
</soapenv:Envelope>';
    }


    private function createSoapRequest($body) {
        date_default_timezone_set('UTC');

        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        // Get current time and apply timestamp adjustments for TRACES API synchronization
        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // TRACES API timestamp synchronization fixes:
        // 1. Subtract a few seconds to account for network delay and server time drift
        $now->modify('-10 seconds');

        // 2. Use longer expiration time (15 minutes instead of 5) as suggested in docs
        $created = $now->format('Y-m-d\TH:i:s\Z');
        $expires = (clone $now)->modify('+15 minutes')->format('Y-m-d\TH:i:s\Z');

        // Log timestamp details for debugging
        \Log::info('TRACES API WS-Security Timestamps', [
            'created' => $created,
            'expires' => $expires,
            'duration_minutes' => 15,
            'server_adjustment' => '-10 seconds for sync'
        ]);

        $passwordDigest = base64_encode(
            sha1($nonceRaw . $created . $this->authKey, true)
        );

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $created . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $created . '</wsu:Created>
                <wsu:Expires>' . $expires . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>' . $body . '</soapenv:Body>
</soapenv:Envelope>';
    }

    private function sendSoapRequest($endpoint, $soapRequest) {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "findChedCertificate"',
            'Content-Length: ' . strlen($soapRequest),
            'User-Agent: ONISPA-Laravel-Client/1.0'
        ];

        // Get configuration from Laravel config
        $tracesConfig = config('services.traces', []);
        $timeout = $tracesConfig['timeout'] ?? 60;
        $verifySSL = $tracesConfig['verify_ssl'] ?? true;

        // Log request for debugging (in storage/logs)
        \Log::info('TRACES API Request', [
            'endpoint' => $endpoint,
            'request_size' => strlen($soapRequest),
            'timeout' => $timeout,
            'verify_ssl' => $verifySSL
        ]);

        $ch = curl_init($endpoint);
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => $soapRequest,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => $verifySSL,
            CURLOPT_SSL_VERIFYHOST => $verifySSL ? 2 : 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_USERAGENT => 'ONISPA-Laravel-Client/1.0',
            CURLOPT_VERBOSE => false, // Disable verbose in production
            CURLOPT_FAILONERROR => false,
            // Add these for better server compatibility
            CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $curlErrno = curl_errno($ch);

        // Get additional curl info for debugging
        $curlInfo = curl_getinfo($ch);
        curl_close($ch);

        // Log response details
        \Log::info('TRACES API Response', [
            'http_code' => $httpCode,
            'response_size' => strlen($response),
            'total_time' => $curlInfo['total_time'] ?? 0,
            'connect_time' => $curlInfo['connect_time'] ?? 0,
            'curl_error' => $curlError,
            'curl_errno' => $curlErrno
        ]);

        if ($curlErrno) {
            throw new \Exception("CURL Error ({$curlErrno}): {$curlError}");
        }

        if ($httpCode != 200 && $httpCode != 500) {
            // Log the full response for debugging
            \Log::error('TRACES API HTTP Error', [
                'http_code' => $httpCode,
                'response' => substr($response, 0, 1000) // First 1000 chars
            ]);
            throw new \Exception("HTTP Error {$httpCode}: " . substr($response, 0, 500));
        }

        // Log the full response for debugging HTTP 500 errors
        if ($httpCode == 500) {
            \Log::error('TRACES API HTTP 500 Error - Full Response', [
                'response' => $response,
                'response_length' => strlen($response)
            ]);
        }

        // Check for authentication errors in the response
        if (strpos($response, 'Unauthenticated\Exception') !== false) {
            \Log::error('TRACES API Authentication Error', [
                'response_preview' => substr($response, 0, 1000)
            ]);
            throw new \Exception('Authentication failed: Invalid credentials or expired session');
        }

        return $response;
    }



    private function parseResponse($xmlResponse) {
    file_put_contents('raw_response.xml', $xmlResponse);

    $dom = new \DOMDocument();
    libxml_use_internal_errors(true);

    if (!$dom->loadXML($xmlResponse)) {
        $errors = libxml_get_errors();
        $errorMessages = array_map(fn($e) => $e->message, $errors);
        throw new \Exception("Failed to parse XML response:\n" . implode("\n", $errorMessages));
    }

    $xpath = new \DOMXPath($dom);
    $xpath->registerNamespace('soap', 'http://schemas.xmlsoap.org/soap/envelope/');
    $xpath->registerNamespace('ched', 'http://ec.europa.eu/tracesnt/certificate/ched/v2');
    $xpath->registerNamespace('cert', 'http://ec.europa.eu/tracesnt/certificate/base/v01');
    $xpath->registerNamespace('base', 'http://ec.europa.eu/sanco/tracesnt/base/v4');
    $xpath->registerNamespace('ns4', 'urn:un:unece:uncefact:data:standard:ReusableAggregateBusinessInformationEntity:21');

    $fault = $xpath->query('//soap:Fault');
    if ($fault->length > 0) {
        $faultCode = $xpath->query('//soap:faultcode', $fault->item(0))->item(0)?->nodeValue ?? 'Unknown';
        $faultString = $xpath->query('//soap:faultstring', $fault->item(0))->item(0)?->nodeValue ?? 'Unknown';
        throw new \Exception("SOAP Fault: [$faultCode] $faultString");
    }

    $certificates = [];
    $certNodes = $xpath->query('//ched:ChedCertificateResult');

    if ($certNodes->length === 0) {
        return [];
    }

    foreach ($certNodes as $cert) {
        $certData = [
            'id' => $xpath->query('./ched:ID', $cert)->item(0)?->nodeValue,
            'type' => $xpath->query('./ched:Type', $cert)->item(0)?->nodeValue,
            'local_reference' => $xpath->query('./ched:LocalReference', $cert)->item(0)?->nodeValue,
            'status' => $xpath->query('./ched:Status', $cert)->item(0)?->nodeValue,
            'status_name' => $xpath->query('./ched:Status/@name', $cert)->item(0)?->nodeValue,
            'bcp_code' => $xpath->query('./ched:BCPCode', $cert)->item(0)?->nodeValue,
            'bcp_unlocode' => $xpath->query('./ched:BCPUnLocode', $cert)->item(0)?->nodeValue,
            'country_of_issuance' => $xpath->query('./ched:CountryOfIssuance', $cert)->item(0)?->nodeValue,
            'country_of_entry' => $xpath->query('./ched:CountryOfEntry', $cert)->item(0)?->nodeValue,
            'country_of_dispatch' => $xpath->query('./ched:CountryOfDispatch', $cert)->item(0)?->nodeValue,
            'country_of_origin' => $xpath->query('./ched:CountryOfOrigin', $cert)->item(0)?->nodeValue,
            'country_of_place_of_destination' => $xpath->query('./ched:CountryOfPlaceOfDestination', $cert)->item(0)?->nodeValue,
            'country_of_consignor' => $xpath->query('./ched:CountryOfConsignor', $cert)->item(0)?->nodeValue,
            'consignor_name' => $xpath->query('./ched:ConsignorName', $cert)->item(0)?->nodeValue,
            'country_of_consignee' => $xpath->query('./ched:CountryOfConsignee', $cert)->item(0)?->nodeValue,
            'consignee_name' => $xpath->query('./ched:ConsigneeName', $cert)->item(0)?->nodeValue,
            'create_date_time' => $xpath->query('./ched:CreateDateTime', $cert)->item(0)?->nodeValue,
            'update_date_time' => $xpath->query('./ched:UpdateDateTime', $cert)->item(0)?->nodeValue,
            'status_change_date_time' => $xpath->query('./ched:StatusChangeDateTime', $cert)->item(0)?->nodeValue,
            'declaration_date_time' => $xpath->query('./ched:DeclarationDateTime', $cert)->item(0)?->nodeValue,
            'decision_date_time' => $xpath->query('./ched:DecisionDateTime', $cert)->item(0)?->nodeValue,
            'prior_notification_date_time' => $xpath->query('./ched:PriorNotificationDateTime', $cert)->item(0)?->nodeValue,
            'commodities' => [],
            'references' => []
        ];

        // Extract commodity classifications
        $commodityNodes = $xpath->query('./ched:CommodityApplicableSPSClassification', $cert);
        foreach ($commodityNodes as $commodity) {
            $commodityData = [
                'system_id' => $xpath->query('./ns4:SystemID', $commodity)->item(0)?->nodeValue,
                'system_name' => $xpath->query('./ns4:SystemName', $commodity)->item(0)?->nodeValue,
                'class_code' => $xpath->query('./ns4:ClassCode', $commodity)->item(0)?->nodeValue,
                'class_names' => []
            ];
            
            $classNames = $xpath->query('./ns4:ClassName', $commodity);
            foreach ($classNames as $className) {
                $lang = $className->getAttribute('languageID') ?: 'en';
                $commodityData['class_names'][$lang] = $className->nodeValue;
            }
            
            $certData['commodities'][] = $commodityData;
        }

        // Extract references
        $refNodes = $xpath->query('./ched:ReferenceSPSReferencedDocument', $cert);
        foreach ($refNodes as $ref) {
            $refData = [
                'issue_date_time' => $xpath->query('./ns4:IssueDateTime', $ref)->item(0)?->nodeValue,
                'type_code' => $xpath->query('./ns4:TypeCode', $ref)->item(0)?->nodeValue,
                'type_name' => $xpath->query('./ns4:TypeCode/@name', $ref)->item(0)?->nodeValue,
                'relationship_type' => $xpath->query('./ns4:RelationshipTypeCode', $ref)->item(0)?->nodeValue,
                'relationship_name' => $xpath->query('./ns4:RelationshipTypeCode/@name', $ref)->item(0)?->nodeValue,
                'id' => $xpath->query('./ns4:ID', $ref)->item(0)?->nodeValue,
                'attachment' => $xpath->query('./ns4:AttachmentBinaryObject/@uri', $ref)->item(0)?->nodeValue,
                'scheme_agency' => $xpath->query('./ns4:ID/@schemeAgencyID', $ref)->item(0)?->nodeValue
            ];
            
            $certData['references'][] = $refData;
        }

        $certificates[] = $certData;
    }

    return $certificates;
}

    public function debugAuthentication() {
        date_default_timezone_set('UTC');
        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new \DateTime('now', new \DateTimeZone('UTC'));
        $createdForDigestAndUsernameToken = $now->format('Y-m-d\TH:i:s\Z');
        $createdForTimestampElement = $now->format('Y-m-d\TH:i:s.v\Z');
        $expiresForTimestampElement = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s.v\Z');

        echo "Debug Authentication:\n";
        echo "Username: " . $this->username . "\n";
        echo "Client ID: " . $this->clientId . "\n";
        echo "Auth Key (first 10 chars): " . substr($this->authKey, 0, 10) . "...\n";
        echo "Auth Key Length: " . strlen($this->authKey) . "\n";
        echo "Nonce (base64): " . $nonce . "\n";
        echo "Created (UsernameToken/Digest - no ms): " . $createdForDigestAndUsernameToken . "\n";
        echo "Created (Timestamp element - with ms): " . $createdForTimestampElement . "\n";
        echo "Expires (Timestamp element - with ms): " . $expiresForTimestampElement . "\n";

        $passwordDigest = base64_encode(
            sha1($nonceRaw . $createdForDigestAndUsernameToken . $this->authKey, true)
        );
        echo "Password Digest: " . $passwordDigest . "\n";

        // Show which endpoint will be used
        $endpoint = $this->useProduction
            ? 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
            : 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2';
        echo "Endpoint: " . $endpoint . "\n";
    }

    public function testAuthenticationMethods() {
        // Test different endpoints
        $endpoints = [
            'training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'acceptance' => 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2',
            'production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];

        // Test different auth keys
        $authKeys = [
            'current' => $this->authKey,
            'alternative' => '0q2JpCSjeGEMUR1qQPXLg15SYiEPi9r6rntcgcXa'
        ];

        // Test different digest calculation methods
        $methods = [
            1 => 'Raw nonce + created + auth_key (current method)',
            2 => 'Raw nonce + created + auth_key (same timestamp format)',
            3 => 'Raw nonce + created + base64_decoded_auth_key',
            4 => 'Base64 nonce + created + auth_key'
        ];

        foreach ($endpoints as $endpointName => $endpoint) {
            echo "\n=== TESTING ENDPOINT: $endpointName ($endpoint) ===\n";

            foreach ($authKeys as $keyName => $authKey) {
                echo "\n--- Testing with $keyName auth key ---\n";

                // Temporarily set the auth key for testing
                $originalAuthKey = $this->authKey;
                $this->authKey = $authKey;

                foreach ($methods as $methodNum => $description) {
                    echo "\nMethod $methodNum ($description):\n";

                    try {
                        $soapRequest = $this->createSoapRequestMethod($methodNum);
                        $response = $this->sendTestRequest($endpoint, $soapRequest, "{$endpointName}_{$keyName}_{$methodNum}");

                        if (strpos($response, 'Unauthenticated\Exception') === false) {
                            echo "✓ SUCCESS! Endpoint: $endpointName, Auth Key: $keyName, Method: $methodNum\n";
                            echo "Response preview: " . substr($response, 0, 200) . "...\n";
                            file_put_contents("successful_{$endpointName}_{$keyName}_{$methodNum}_response.xml", $response);

                            // Restore original auth key
                            $this->authKey = $originalAuthKey;
                            return ['endpoint' => $endpointName, 'authKey' => $keyName, 'method' => $methodNum];
                        } else {
                            echo "✗ Failed - Unauthenticated\Exception\n";
                        }

                    } catch (\Exception $e) {
                        echo "✗ Failed with \Exception: " . $e->getMessage() . "\n";
                    }
                }

                // Restore original auth key
                $this->authKey = $originalAuthKey;
            }
        }

        return false; // No combination worked
    }

    private function createSoapRequestMethod($method) {
        date_default_timezone_set('UTC');
        $nonceRaw = random_bytes(16);
        $nonce = base64_encode($nonceRaw);

        $now = new \DateTime('now', new \DateTimeZone('UTC'));

        // Calculate password digest based on method
        switch ($method) {
            case 1: // Current method - Raw nonce + created + auth_key (different timestamp formats)
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $now->format('Y-m-d\TH:i:s.v\Z');
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s.v\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . $this->authKey, true)
                );
                break;

            case 2: // Same timestamp format for both
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . $this->authKey, true)
                );
                break;

            case 3: // Raw nonce + created + base64_decoded_auth_key
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonceRaw . $createdForDigest . base64_decode($this->authKey), true)
                );
                break;

            case 4: // Base64 nonce + created + auth_key
                $createdForDigest = $now->format('Y-m-d\TH:i:s\Z');
                $createdForTimestamp = $createdForDigest;
                $expiresForTimestamp = (clone $now)->modify('+5 minutes')->format('Y-m-d\TH:i:s\Z');
                $passwordDigest = base64_encode(
                    sha1($nonce . $createdForDigest . $this->authKey, true)
                );
                break;

            default:
                throw new \Exception("Unknown method: $method");
        }

        echo "Method $method - Created for digest: $createdForDigest\n";
        echo "Method $method - Password digest: $passwordDigest\n";

        return '<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
    xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
    xmlns:base="http://ec.europa.eu/sanco/tracesnt/base/v4"
    xmlns:cert="http://ec.europa.eu/tracesnt/certificate/base/v01"
    xmlns:ched="http://ec.europa.eu/tracesnt/certificate/ched/v2">
    <soapenv:Header>
        <wsse:Security
            xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"
            xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"
            soapenv:mustUnderstand="1">
            <wsse:UsernameToken wsu:Id="UsernameToken-' . uniqid() . '">
                <wsse:Username>' . htmlspecialchars($this->username) . '</wsse:Username>
                <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordDigest">' . $passwordDigest . '</wsse:Password>
                <wsse:Nonce EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary">' . $nonce . '</wsse:Nonce>
                <wsu:Created>' . $createdForDigest . '</wsu:Created>
            </wsse:UsernameToken>
            <wsu:Timestamp wsu:Id="TS-' . uniqid() . '">
                <wsu:Created>' . $createdForTimestamp . '</wsu:Created>
                <wsu:Expires>' . $expiresForTimestamp . '</wsu:Expires>
            </wsu:Timestamp>
        </wsse:Security>
        <base:LanguageCode>en</base:LanguageCode>
        <base:WebServiceClientId>' . htmlspecialchars($this->clientId) . '</base:WebServiceClientId>
    </soapenv:Header>
    <soapenv:Body>
        <ched:FindChedCertificateRequest>
            <ched:Type>P</ched:Type>
            <ched:Status>70</ched:Status>
            <ched:CountryOfIssuance>MR</ched:CountryOfIssuance>
            <ched:UpdateDateTimeRange>
                <base:From>2024-01-01T00:00:00Z</base:From>
                <base:To>2024-06-20T23:59:59Z</base:To>
            </ched:UpdateDateTimeRange>
        </ched:FindChedCertificateRequest>
        <ched:PageSize>50</ched:PageSize>
        <ched:Offset>0</ched:Offset>
    </soapenv:Body>
</soapenv:Envelope>';
    }

    private function sendTestRequest($endpoint, $soapRequest, $methodNum) {
        $headers = [
            'Content-Type: text/xml; charset=utf-8',
            'SOAPAction: "findChedCertificate"',
            'Content-Length: ' . strlen($soapRequest)
        ];

        file_put_contents("test_method_{$methodNum}_request.xml", $soapRequest);

        $ch = curl_init($endpoint);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $soapRequest);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            throw new \Exception('Curl error: ' . curl_error($ch));
        }

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        file_put_contents("test_method_{$methodNum}_response.xml", $response);

        if ($httpCode != 200 && $httpCode != 500) {
            throw new \Exception('HTTP error: ' . $httpCode);
        }

        return $response;
    }
}

try {

    $client = new TracesNtChedClient(
        'n00385tm',
        '7qzFRburdCKThQqGyoefyTN6xuDFIXVC3FUSDdFh',  // Working auth key
        'onispa-mr',
        true  
    );

    echo "=== TESTING WITH WORKING CONFIGURATION ===\n";
    echo "Using working auth key and Method 2 authentication...\n";

    // Test the actual certificate fetching
    echo "\n=== ATTEMPTING TO FETCH CERTIFICATES ===\n";

    // YYYY-MM-DD
$certificates = $client->getValidatedFishCertificates('2025-01-01', '2025-01-02');

    echo "SUCCESS! Validated Fish Export Certificates found: " . count($certificates) . "\n";
    if (!empty($certificates)) {
        echo "\nFirst certificate details:\n";
        print_r($certificates[0]);

        echo "\nAll certificates:\n";
        foreach ($certificates as $index => $cert) {
            echo "Certificate " . ($index + 1) . ":\n";
            echo "  ID: " . ($cert['id'] ?? 'N/A') . "\n";
            echo "  Type: " . ($cert['type'] ?? 'N/A') . "\n";
            echo "  Status: " . ($cert['status'] ?? 'N/A') . "\n";
            echo "  Validation Date: " . ($cert['validation_date'] ?? 'N/A') . "\n";
            echo "  Exporter: " . ($cert['exporter'] ?? 'N/A') . "\n";
            echo "  Country of Origin: " . ($cert['country_of_origin'] ?? 'N/A') . "\n";
            echo "\n";
        }
    } else {
        echo "No certificates found for the specified date range.\n";
    }

    // Optionally, you can still run the comprehensive test
    echo "\n=== OPTIONAL: COMPREHENSIVE AUTHENTICATION TEST ===\n";
    echo "Uncomment the lines below if you want to run the full test again:\n";
    /*
    $successfulConfig = $client->testAuthenticationMethods();

    if ($successfulConfig) {
        echo "\n🎉 SUCCESS! Found working configuration:\n";
        echo "Endpoint: " . $successfulConfig['endpoint'] . "\n";
        echo "Auth Key: " . $successfulConfig['authKey'] . "\n";
        echo "Method: " . $successfulConfig['method'] . "\n";
        echo "\nNow you need to:\n";
        echo "1. Update your auth key if using 'alternative'\n";
        echo "2. Update your endpoint URL based on the working endpoint\n";
        echo "3. Update your authentication method in the createSoapRequest function\n";

        // Show the working endpoint URL
        $workingEndpoints = [
            'training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'acceptance' => 'https://webgate.acceptance.ec.europa.eu/tracesnt/ws/ChedCertificateServiceV2',
            'production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];
        echo "\nWorking endpoint URL: " . $workingEndpoints[$successfulConfig['endpoint']] . "\n";

    } else {
        echo "\n❌ None of the authentication methods worked.\n";
        echo "Please check:\n";
        echo "1. Your username is correct: n00385tm\n";
        echo "2. Your WebService authentication key is current and matches what's in your TRACES profile\n";
        echo "3. Your client ID matches what TRACES assigned: onispa-mr\n";
        echo "4. Your WebService access is still active\n";
        echo "5. You're using the correct endpoint (training vs production)\n";
        echo "6. Contact TRACES support to verify your credentials\n";
    }
    */

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";

    if (file_exists('request.xml')) echo "\nRequest XML saved to request.xml\n";
    if (file_exists('response.xml')) echo "Response XML saved to response.xml\n";
    if (file_exists('raw_response.xml')) echo "Raw response saved to raw_response.xml\n";
}
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ApiLog extends Model
{
    protected $fillable = [
        'type', // 'traces_outgoing' or 'our_incoming'
        'endpoint',
        'method',
        'request_data',
        'response_data',
        'status_code',
        'status_message',
        'duration_ms',
        'user_agent',
        'ip_address',
        'date_range_start',
        'date_range_end',
        'certificates_count',
        'error_message',
        'success'
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'success' => 'boolean',
        'date_range_start' => 'date',
        'date_range_end' => 'date',
        'duration_ms' => 'decimal:2',
        'certificates_count' => 'integer'
    ];

    public function scopeTracesOutgoing($query)
    {
        return $query->where('type', 'traces_outgoing');
    }

    public function scopeOurIncoming($query)
    {
        return $query->where('type', 'our_incoming');
    }

    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }
} 
<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class FixServerTime extends Command
{
    protected $signature = 'server:fix-time {--dry-run : Show what would be done without executing}';
    protected $description = 'Fix server time synchronization issues';

    public function handle()
    {
        $this->info('🕐 Server Time Diagnostic and Fix Tool');
        $this->newLine();

        // Current system information
        $this->info('📊 Current System Information:');
        $this->line('PHP Time: ' . date('Y-m-d H:i:s T'));
        $this->line('System Date: ' . trim(shell_exec('date') ?? 'unavailable'));
        $this->line('Timezone: ' . date_default_timezone_get());
        $this->line('Unix Timestamp: ' . time());
        $this->newLine();

        // Check if time is wrong (year 2025 instead of 2024)
        $currentYear = (int) date('Y');
        $expectedYear = 2024;
        
        if ($currentYear > $expectedYear) {
            $this->error("❌ TIME ERROR DETECTED!");
            $this->error("Current year: {$currentYear}");
            $this->error("Expected year: {$expectedYear}");
            $this->error("Server time is " . ($currentYear - $expectedYear) . " year(s) ahead!");
            $this->newLine();

            if ($this->option('dry-run')) {
                $this->info('🔍 DRY RUN - Commands that would be executed:');
                $this->showFixCommands();
                return 0;
            }

            if ($this->confirm('Do you want to attempt to fix the server time?')) {
                $this->attemptTimeFix();
            }
        } else {
            $this->info('✅ Server time appears to be correct');
        }

        // Test NTP synchronization
        $this->testNtpSync();

        // Test TRACES API connectivity with current time
        $this->testTracesConnectivity();

        return 0;
    }

    private function showFixCommands()
    {
        $correctDate = date('Y-m-d H:i:s', time() - (365 * 24 * 60 * 60));
        
        $this->line('1. sudo timedatectl set-ntp false');
        $this->line("2. sudo date -s '{$correctDate}'");
        $this->line('3. sudo timedatectl set-ntp true');
        $this->line('4. sudo systemctl restart ntp || sudo systemctl restart chrony');
        $this->newLine();
    }

    private function attemptTimeFix()
    {
        $this->info('🔧 Attempting to fix server time...');

        // Calculate correct time (subtract 1 year)
        $correctTimestamp = time() - (365 * 24 * 60 * 60);
        $correctDate = date('Y-m-d H:i:s', $correctTimestamp);

        $commands = [
            'timedatectl set-ntp false' => 'Disabling NTP temporarily',
            "date -s '{$correctDate}'" => 'Setting correct date',
            'timedatectl set-ntp true' => 'Re-enabling NTP',
            'systemctl restart ntp 2>/dev/null || systemctl restart chrony 2>/dev/null || true' => 'Restarting time service'
        ];

        foreach ($commands as $command => $description) {
            $this->line("Executing: {$description}");
            
            $output = shell_exec("sudo {$command} 2>&1");
            if ($output) {
                $this->line("Output: " . trim($output));
            }
        }

        $this->newLine();
        $this->info('✅ Time fix attempted. New system time:');
        $this->line('System Date: ' . trim(shell_exec('date') ?? 'unavailable'));
        $this->line('PHP Time: ' . date('Y-m-d H:i:s T'));
    }

    private function testNtpSync()
    {
        $this->info('🔄 Testing NTP Synchronization:');

        // Check timedatectl status
        $timedatectl = shell_exec('timedatectl status 2>/dev/null');
        if ($timedatectl) {
            $lines = explode("\n", $timedatectl);
            foreach ($lines as $line) {
                if (strpos($line, 'NTP synchronized') !== false || 
                    strpos($line, 'System clock synchronized') !== false) {
                    $this->line($line);
                }
            }
        }

        // Test NTP servers
        $ntpServers = ['pool.ntp.org', 'time.google.com', 'time.cloudflare.com'];
        foreach ($ntpServers as $server) {
            $result = shell_exec("ping -c 1 -W 2 {$server} 2>/dev/null");
            if ($result && strpos($result, '1 received') !== false) {
                $this->line("✅ Can reach {$server}");
            } else {
                $this->line("❌ Cannot reach {$server}");
            }
        }
        $this->newLine();
    }

    private function testTracesConnectivity()
    {
        $this->info('🌐 Testing TRACES API Connectivity:');

        $endpoints = [
            'production' => 'https://webgate.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2',
            'training' => 'https://webgate.training.ec.europa.eu:443/tracesnt/ws/ChedCertificateServiceV2'
        ];

        foreach ($endpoints as $name => $url) {
            $this->line("Testing {$name} endpoint...");
            
            $ch = curl_init($url);
            curl_setopt_array($ch, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 10,
                CURLOPT_CONNECTTIMEOUT => 5,
                CURLOPT_SSL_VERIFYPEER => true,
                CURLOPT_NOBODY => true, // HEAD request only
                CURLOPT_FOLLOWLOCATION => true
            ]);

            $result = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error) {
                $this->line("❌ {$name}: {$error}");
            } else {
                $this->line("✅ {$name}: HTTP {$httpCode} (reachable)");
            }
        }
        $this->newLine();

        $this->info('💡 Next Steps:');
        $this->line('1. Fix server time if needed');
        $this->line('2. Run: php artisan traces:test --debug');
        $this->line('3. Check Laravel logs for detailed error messages');
    }
}

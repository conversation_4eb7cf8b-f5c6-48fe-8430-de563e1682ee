version: "3.8"

services:
  onispa_server:
    container_name: 'onispa_server'
    build: .
    volumes:
      - './:/var/www/html'
      - '/var/www/html/vendor'
    ports:
      - "8011:80"
    depends_on:
      - onispa_db
    restart: always
  onispa_db:
    image: postgis/postgis:11-2.5
    container_name: 'onispa_db'
    restart: always
    ports:
      - "5411:5432"
    environment:
      POSTGRES_PASSWORD: adminonispa
  onsipaPgadmin:
    image: dpage/pgadmin4
    container_name: 'onsipaPgadmin'
    restart: always
    ports:
      - "5011:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: adminonispa
    depends_on:
      - onispa_db


@extends('layouts.app')

@section('title', 'API & System Logs')

@section('content')
<div class="space-y-6">
    <!-- Logs Header -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">API & System Activity Logs</h3>
                <p class="text-gray-600">Monitor TRACES API calls, incoming requests, and system activities</p>
            </div>
            
            <div class="flex items-center space-x-3">
                <select id="logType" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="all" {{ $type === 'all' ? 'selected' : '' }}>All Logs</option>
                    <option value="traces" {{ $type === 'traces' ? 'selected' : '' }}>TRACES API Calls</option>
                    <option value="incoming" {{ $type === 'incoming' ? 'selected' : '' }}>Incoming Requests</option>
                    <option value="system" {{ $type === 'system' ? 'selected' : '' }}>System Logs</option>
                </select>
                
                <select id="logDays" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                    <option value="1" {{ $days == 1 ? 'selected' : '' }}>Last 24 hours</option>
                    <option value="7" {{ $days == 7 ? 'selected' : '' }}>Last 7 days</option>
                    <option value="30" {{ $days == 30 ? 'selected' : '' }}>Last 30 days</option>
                    <option value="90" {{ $days == 90 ? 'selected' : '' }}>Last 90 days</option>
                </select>
                
                <button 
                    onclick="refreshLogs()"
                    class="bg-gradient-to-r from-primary-600 to-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:from-primary-700 hover:to-blue-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200"
                >
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh
                </button>
            </div>
        </div>
    </div>
    
    <!-- TRACES API Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-paper-plane text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">TRACES API Calls</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $tracesStats['total_calls'] }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $tracesStats['success_rate'] }}%</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-certificate text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Certificates Fetched</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($tracesStats['total_certificates_fetched']) }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p class="text-2xl font-bold text-gray-900">{{ round($tracesStats['average_duration'] ?? 0) }}ms</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Incoming API Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-download text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Incoming Requests</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $incomingStats['total_requests'] }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Success Rate</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $incomingStats['success_rate'] }}%</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Failed Requests</p>
                    <p class="text-2xl font-bold text-gray-900">{{ $incomingStats['failed_requests'] }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-tachometer-alt text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p class="text-2xl font-bold text-gray-900">{{ round($incomingStats['average_duration'] ?? 0) }}ms</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- API Logs Table -->
    @if($type !== 'system')
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">
                    @if($type === 'traces')
                        TRACES API Call Logs
                    @elseif($type === 'incoming')
                        Incoming API Request Logs
                    @else
                        All API Logs
                    @endif
                </h3>
                
                <div class="text-sm text-gray-500">
                    Showing {{ $apiLogs->total() }} entries
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Timestamp
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Type
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Duration
                        </th>
                        @if($type !== 'traces')
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Endpoint
                        </th>
                        @endif
                        @if($type === 'traces')
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date Range
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Certificates
                        </th>
                        @endif
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($apiLogs as $log)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $log->created_at->format('M d, Y H:i:s') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($log->type === 'traces_outgoing')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <i class="fas fa-paper-plane mr-1"></i>
                                    TRACES API
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                    <i class="fas fa-download mr-1"></i>
                                    Incoming
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($log->success)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Success
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <i class="fas fa-times-circle mr-1"></i>
                                    Failed
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $log->duration_ms }}ms
                        </td>
                        @if($type !== 'traces')
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <div class="max-w-xs truncate" title="{{ $log->endpoint }}">
                                {{ $log->endpoint }}
                            </div>
                        </td>
                        @endif
                        @if($type === 'traces')
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            @if($log->date_range_start && $log->date_range_end)
                                {{ $log->date_range_start->format('M d') }} - {{ $log->date_range_end->format('M d, Y') }}
                            @else
                                N/A
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ number_format($log->certificates_count) }}
                        </td>
                        @endif
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button 
                                onclick="showLogDetails({{ $log->id }})"
                                class="text-primary-600 hover:text-primary-700 font-medium"
                            >
                                <i class="fas fa-eye mr-1"></i>
                                Details
                            </button>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="{{ $type === 'traces' ? 7 : 6 }}" class="px-6 py-4 text-center text-gray-500">
                            No API logs found for the selected criteria.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if($apiLogs->hasPages())
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $apiLogs->links() }}
        </div>
        @endif
    </div>
    @endif
    
    <!-- System Logs -->

</div>

<!-- Log Details Modal -->
<div id="logModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">API Log Details</h3>
                <button onclick="closeLogModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="logDetails" class="space-y-4">
                <!-- Log details will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Filter functionality
document.getElementById('logType').addEventListener('change', function() {
    const type = this.value;
    const days = document.getElementById('logDays').value;
    window.location.href = `{{ route('logs') }}?type=${type}&days=${days}`;
});

document.getElementById('logDays').addEventListener('change', function() {
    const type = document.getElementById('logType').value;
    const days = this.value;
    window.location.href = `{{ route('logs') }}?type=${type}&days=${days}`;
});

function refreshLogs() {
    location.reload();
}

// Log details modal
function showLogDetails(logId) {
    fetch(`/api/logs/${logId}`)
        .then(response => response.json())
        .then(log => {
            const detailsHtml = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Basic Information</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Type:</span> 
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${log.type === 'traces_outgoing' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'}">
                                    <i class="fas ${log.type === 'traces_outgoing' ? 'fa-paper-plane' : 'fa-download'} mr-1"></i>
                                    ${log.type === 'traces_outgoing' ? 'TRACES API' : 'Incoming Request'}
                                </span>
                            </div>
                            <div><span class="font-medium">Status:</span> 
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${log.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    <i class="fas ${log.success ? 'fa-check-circle' : 'fa-times-circle'} mr-1"></i>
                                    ${log.success ? 'Success' : 'Failed'}
                                </span>
                            </div>
                            <div><span class="font-medium">Method:</span> ${log.method || 'N/A'}</div>
                            <div><span class="font-medium">Duration:</span> ${log.duration_ms}ms</div>
                            <div><span class="font-medium">Status Code:</span> ${log.status_code || 'N/A'}</div>
                            <div><span class="font-medium">Created:</span> ${new Date(log.created_at).toLocaleString()}</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Request Details</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Endpoint:</span> <code class="bg-gray-100 px-1 rounded text-sm">${log.endpoint || 'N/A'}</code></div>
                            ${log.date_range_start ? `<div><span class="font-medium">Date Range:</span> ${log.date_range_start} to ${log.date_range_end}</div>` : ''}
                            ${log.certificates_count ? `<div><span class="font-medium">Certificates:</span> ${log.certificates_count}</div>` : ''}
                            ${log.ip_address ? `<div><span class="font-medium">IP Address:</span> ${log.ip_address}</div>` : ''}
                            ${log.user_agent ? `<div><span class="font-medium">User Agent:</span> <div class="text-xs bg-gray-100 p-2 rounded mt-1">${log.user_agent}</div></div>` : ''}
                        </div>
                    </div>
                    
                    ${log.request_data ? `
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">Request Data</h4>
                        <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">${JSON.stringify(log.request_data, null, 2)}</pre>
                    </div>
                    ` : ''}
                    
                    ${log.response_data ? `
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">Response Data</h4>
                        <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-40">${JSON.stringify(log.response_data, null, 2)}</pre>
                    </div>
                    ` : ''}
                    
                    ${log.error_message ? `
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">Error Message</h4>
                        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">${log.error_message}</div>
                    </div>
                    ` : ''}
                </div>
            `;
            
            document.getElementById('logDetails').innerHTML = detailsHtml;
            document.getElementById('logModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error fetching log details:', error);
            alert('Error loading log details');
        });
}

function closeLogModal() {
    document.getElementById('logModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('logModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeLogModal();
    }
});
</script>
@endpush 
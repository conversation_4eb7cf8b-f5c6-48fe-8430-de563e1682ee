@extends('layouts.app')

@section('title', 'API Parameters')

@section('content')
<div class="space-y-6">
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-2">TRACES API Configuration</h3>
            <p class="text-gray-600">Configure the connection parameters for the TRACES EU API integration.</p>
        </div>
        
        <form method="POST" action="{{ route('api-parameters.update') }}" class="space-y-6">
            @csrf
            
            <!-- Authentication Section -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-key text-primary-600 mr-2"></i>
                    Authentication Parameters
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            Username
                        </label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            value="{{ $parameters['username'] }}"
                            required
                            class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            placeholder="Enter TRACES API username"
                        >
                        <p class="mt-1 text-sm text-gray-500">Your TRACES API username</p>
                    </div>
                    
                    <div>
                        <label for="auth_key" class="block text-sm font-medium text-gray-700 mb-2">
                            Authentication Key
                        </label>
                        <div class="relative">
                            <input 
                                type="password" 
                                id="auth_key" 
                                name="auth_key" 
                                value="{{ $parameters['auth_key'] }}"
                                required
                                class="block w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                placeholder="Enter authentication key"
                            >
                            <button 
                                type="button" 
                                onclick="togglePassword('auth_key')"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="auth_key_toggle"></i>
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Your TRACES API authentication key</p>
                    </div>
                </div>
            </div>
            
            <!-- Client Configuration -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-cog text-primary-600 mr-2"></i>
                    Client Configuration
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="client_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Client ID
                        </label>
                        <input 
                            type="text" 
                            id="client_id" 
                            name="client_id" 
                            value="{{ $parameters['client_id'] }}"
                            required
                            class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            placeholder="Enter client ID"
                        >
                        <p class="mt-1 text-sm text-gray-500">Your TRACES API client identifier</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Environment
                        </label>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <input 
                                    type="radio" 
                                    name="use_production" 
                                    value="1" 
                                    {{ $parameters['use_production'] ? 'checked' : '' }}
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                                >
                                <span class="ml-2 text-sm text-gray-700">Production</span>
                            </label>
                            <label class="flex items-center">
                                <input 
                                    type="radio" 
                                    name="use_production" 
                                    value="0" 
                                    {{ !$parameters['use_production'] ? 'checked' : '' }}
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                                >
                                <span class="ml-2 text-sm text-gray-700">Training</span>
                            </label>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Select the API environment to use</p>
                    </div>
                </div>
            </div>
            
            <!-- Endpoints Information -->
            <div class="border-b border-gray-200 pb-6">
                <h4 class="text-md font-semibold text-gray-900 mb-4 flex items-center">
                    <i class="fas fa-link text-primary-600 mr-2"></i>
                    API Endpoints
                </h4>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Production Endpoint
                        </label>
                        <div class="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                            <code class="text-sm text-gray-700 break-all">{{ $parameters['endpoint_production'] }}</code>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Live TRACES API endpoint</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Training Endpoint
                        </label>
                        <div class="bg-gray-50 px-3 py-2 rounded-lg border border-gray-200">
                            <code class="text-sm text-gray-700 break-all">{{ $parameters['endpoint_training'] }}</code>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Test environment endpoint</p>
                    </div>
                </div>
            </div>
            
            <!-- Test Connection -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
                    <div class="text-sm text-blue-700">
                        <p class="font-medium">Connection Testing</p>
                        <p>After updating the parameters, you can test the connection by fetching certificates from the Certificates page.</p>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4 pt-6">
                <button 
                    type="button" 
                    onclick="resetForm()"
                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
                >
                    Reset
                </button>
                
                <button 
                    type="submit" 
                    class="bg-gradient-to-r from-primary-600 to-blue-600 text-white py-2 px-6 rounded-lg font-medium hover:from-primary-700 hover:to-blue-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200"
                >
                    <i class="fas fa-save mr-2"></i>
                    Save Configuration
                </button>
            </div>
        </form>
    </div>
    
    <!-- Current Configuration Summary -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Configuration Summary</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Username:</span>
                    <span class="text-sm text-gray-900">{{ $parameters['username'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Client ID:</span>
                    <span class="text-sm text-gray-900">{{ $parameters['client_id'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Environment:</span>
                    <span class="text-sm text-gray-900">
                        @if($parameters['use_production'])
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle mr-1"></i>
                                Production
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-flask mr-1"></i>
                                Training
                            </span>
                        @endif
                    </span>
                </div>
            </div>
            
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Active Endpoint:</span>
                    <span class="text-sm text-gray-900">
                        @if($parameters['use_production'])
                            Production
                        @else
                            Training
                        @endif
                    </span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Last Updated:</span>
                    <span class="text-sm text-gray-900">Not available</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm font-medium text-gray-600">Status:</span>
                    <span class="text-sm text-gray-900">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Configured
                        </span>
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = document.getElementById(fieldId + '_toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

function resetForm() {
    if (confirm('Are you sure you want to reset the form? This will restore the original values.')) {
        location.reload();
    }
}
</script>
@endpush 
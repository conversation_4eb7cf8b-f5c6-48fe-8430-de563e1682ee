@extends('layouts.app')

@section('title', 'Certificats')

@section('content')
<div class="space-y-6">
    <!-- Indicateurs de Période -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Informations sur la Période des Certificats</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-plus text-white"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-blue-900">Dernier Certificat</p>
                        <p class="text-lg font-bold text-blue-700">
                            {{ $lastCertificateDate ? \Carbon\Carbon::parse($lastCertificateDate)->format('d M Y') : 'Aucun certificat' }}
                        </p>
                        @if($lastCertificateDate)
                            <p class="text-xs text-blue-600">
                                {{ \Carbon\Carbon::parse($lastCertificateDate)->diffForHumans() }}
                            </p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calendar-minus text-white"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-900">Premier Certificat</p>
                        <p class="text-lg font-bold text-green-700">
                            {{ $earliestCertificateDate ? \Carbon\Carbon::parse($earliestCertificateDate)->format('d M Y') : 'Aucun certificat' }}
                        </p>
                        @if($earliestCertificateDate)
                            <p class="text-xs text-green-600">
                                {{ \Carbon\Carbon::parse($earliestCertificateDate)->diffForHumans() }}
                            </p>
                        @endif
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-purple-900">Activité Récente</p>
                        <div class="text-xs text-purple-600 space-y-1">
                            <div>Aujourd'hui : {{ $todayCount }} certificats</div>
                            <div>Hier : {{ $yesterdayCount }} certificats</div>
                            <div>7 derniers jours : {{ $lastWeekCount }} certificats</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if($lastCertificateDate)
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-start">
                <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-3"></i>
                <div class="text-sm text-yellow-800">
                    <p class="font-medium">💡 Période Suggérée :</p>
                    <p>Le dernier certificat a été récupéré le <strong>{{ \Carbon\Carbon::parse($lastCertificateDate)->format('d M Y') }}</strong>. 
                    Vous pouvez choisir une date de début à partir du <strong>{{ \Carbon\Carbon::parse($lastCertificateDate)->addDay()->format('d M Y') }}</strong> 
                    pour récupérer les certificats suivants.</p>
                </div>
            </div>
        </div>
        @endif
    </div>
        <!-- Section de Récupération des Certificats -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Récupérer les Certificats depuis l'API TRACES</h3>
        
        <form method="POST" action="{{ route('certificates.fetch') }}" class="space-y-4">
            @csrf
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Date de Début
                    </label>
                    <input 
                        type="date" 
                        id="start_date" 
                        name="start_date" 
                        required
                        value="{{ $lastCertificateDate ? \Carbon\Carbon::parse($lastCertificateDate)->addDay()->format('Y-m-d') : date('Y-m-d', strtotime('-7 days')) }}"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                    @if($lastCertificateDate)
                        <p class="mt-1 text-xs text-gray-500">
                            Suggéré : {{ \Carbon\Carbon::parse($lastCertificateDate)->addDay()->format('d M Y') }}
                        </p>
                    @endif
                </div>
                
                <div>
                    <label for="end_date" class="block text-sm font-medium text-gray-700 mb-2">
                        Date de Fin
                    </label>
                    <input 
                        type="date" 
                        id="end_date" 
                        name="end_date" 
                        required
                        value="{{ date('Y-m-d') }}"
                        class="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                    <p class="mt-1 text-xs text-gray-500">Aujourd'hui</p>
                </div>
                
                <div class="flex items-end">
                    <button 
                        type="submit" 
                        class="w-full bg-gradient-to-r from-primary-600 to-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:from-primary-700 hover:to-blue-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105"
                    >
                        <i class="fas fa-download mr-2"></i>
                        Récupérer les Certificats
                    </button>
                </div>
            </div>
        </form>
        
        <!-- Boutons de Plages de Dates Rapides -->
        <div class="mt-4 pt-4 border-t border-gray-200">
            <p class="text-sm font-medium text-gray-700 mb-3">Plages de Dates Rapides :</p>
            <div class="flex flex-wrap gap-2">
                <button 
                    type="button" 
                    onclick="setDateRange('today')"
                    class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                >
                    Aujourd'hui
                </button>
                <button 
                    type="button" 
                    onclick="setDateRange('yesterday')"
                    class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                >
                    Hier
                </button>
                <button 
                    type="button" 
                    onclick="setDateRange('last3days')"
                    class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                >
                    3 Derniers Jours
                </button>
                <button 
                    type="button" 
                    onclick="setDateRange('last7days')"
                    class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                >
                    7 Derniers Jours
                </button>
                <button 
                    type="button" 
                    onclick="setDateRange('last30days')"
                    class="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-md transition-colors"
                >
                    30 Derniers Jours
                </button>
                @if($lastCertificateDate)
                <button 
                    type="button" 
                    onclick="setDateRange('sinceLast')"
                    class="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded-md transition-colors font-medium"
                >
                    Depuis le Dernier Certificat
                </button>
                @endif
            </div>
        </div>
    </div>
    <!-- Liste des Certificats -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Certificats ({{ $certificates->total() }})</h3>
                
                <div class="flex items-center space-x-3">
                    <button 
                        id="selectAllBtn" 
                        class="text-sm text-primary-600 hover:text-primary-700 font-medium"
                    >
                        Tout Sélectionner
                    </button>
                    
                    <form method="POST" action="{{ route('certificates.send') }}" id="sendForm" class="hidden">
                        @csrf
                        <input type="hidden" name="selected_certificates" id="selectedCertificates">
                        <button 
                            type="submit" 
                            class="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-4 rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200"
                        >
                            <i class="fas fa-paper-plane mr-2"></i>
                            Envoyer Sélectionnés (<span id="selectedCount">0</span>)
                        </button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID du Certificat
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Stocké dans notre système	
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Expéditeur
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Destinataire
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Validé par
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Créé le
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($certificates as $certificate)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <input 
                                type="checkbox" 
                                name="selected_certificates[]" 
                                value="{{ $certificate->id }}" 
                                class="certificate-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                            >
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $certificate->certificate_id }}</div>
                            <div class="text-sm text-gray-500">{{ $certificate->local_reference }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($certificate->status == 'sent')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Envoyé
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>
                                    En attente
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $certificate->consignor_name }}</div>
                            <div class="text-sm text-gray-500">{{ $certificate->country_of_consignor }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $certificate->consignee_name }}</div>
                            <div class="text-sm text-gray-500">{{ $certificate->country_of_consignee }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            @if($certificate->validator)
                                <div class="flex items-center">
                                    <i class="fas fa-user-check text-green-500 mr-2"></i>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $certificate->validator->name }}</div>
                                        @if($certificate->validated_at)
                                            <div class="text-xs text-gray-500">{{ $certificate->validated_at->format('d/m/Y H:i') }}</div>
                                        @endif
                                    </div>
                                </div>
                            @else
                                <span class="text-gray-400">
                                    <i class="fas fa-minus mr-1"></i>
                                    Non validé
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $certificate->created_at->format('d M Y H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button 
                                onclick="showCertificateDetails({{ $certificate->id }})"
                                class="text-primary-600 hover:text-primary-700 font-medium"
                            >
                                <i class="fas fa-eye mr-1"></i>
                                Voir Détails
                            </button>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                            Aucun certificat trouvé. Utilisez le formulaire ci-dessus pour récupérer des certificats via l'API TRACES.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
        
        @if($certificates->hasPages())
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $certificates->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Modal Détail Certificat -->
<div id="certificateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Détails du Certificat</h3>
                <button onclick="closeCertificateModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="certificateDetails" class="space-y-4">
                <!-- Les détails du certificat seront chargés ici -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
let selectedCertificates = [];

// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// Individual checkbox functionality
document.querySelectorAll('.certificate-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.certificate-checkbox:checked');
    const count = checkboxes.length;
    document.getElementById('selectedCount').textContent = count;
    
    if (count > 0) {
        document.getElementById('sendForm').classList.remove('hidden');
    } else {
        document.getElementById('sendForm').classList.add('hidden');
    }
    
    // Update hidden input
    const selectedIds = Array.from(checkboxes).map(cb => cb.value);
    document.getElementById('selectedCertificates').value = JSON.stringify(selectedIds);
}

// Select all button
document.getElementById('selectAllBtn').addEventListener('click', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    selectAllCheckbox.checked = !selectAllCheckbox.checked;
    selectAllCheckbox.dispatchEvent(new Event('change'));
});

// Quick date range functions
function setDateRange(range) {
    const today = new Date();
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    
    switch(range) {
        case 'today':
            startDateInput.value = today.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];
            break;
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            startDateInput.value = yesterday.toISOString().split('T')[0];
            endDateInput.value = yesterday.toISOString().split('T')[0];
            break;
        case 'last3days':
            const threeDaysAgo = new Date(today);
            threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
            startDateInput.value = threeDaysAgo.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];
            break;
        case 'last7days':
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            startDateInput.value = sevenDaysAgo.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];
            break;
        case 'last30days':
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];
            break;
        case 'sinceLast':
            // This will be set to the day after the last certificate
            const lastCertDate = '{{ $lastCertificateDate }}';
            if (lastCertDate) {
                const nextDay = new Date(lastCertDate);
                nextDay.setDate(nextDay.getDate() + 1);
                startDateInput.value = nextDay.toISOString().split('T')[0];
                endDateInput.value = today.toISOString().split('T')[0];
            }
            break;
    }
}

// Certificate details modal
function showCertificateDetails(certificateId) {
    fetch(`/certificates/${certificateId}`)
        .then(response => response.json())
        .then(certificate => {
            const detailsHtml = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Basic Information</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Certificate ID:</span> ${certificate.certificate_id}</div>
                            <div><span class="font-medium">Local Reference:</span> ${certificate.local_reference || 'N/A'}</div>
                            <div><span class="font-medium">Type:</span> ${certificate.type || 'N/A'}</div>
                            <div><span class="font-medium">Status:</span> ${certificate.status_name || certificate.status || 'N/A'}</div>
                            <div><span class="font-medium">BCP Code:</span> ${certificate.bcp_code || 'N/A'}</div>
                            <div><span class="font-medium">BCP UNLOCODE:</span> ${certificate.bcp_unlocode || 'N/A'}</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Countries</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Issuance:</span> ${certificate.country_of_issuance || 'N/A'}</div>
                            <div><span class="font-medium">Entry:</span> ${certificate.country_of_entry || 'N/A'}</div>
                            <div><span class="font-medium">Dispatch:</span> ${certificate.country_of_dispatch || 'N/A'}</div>
                            <div><span class="font-medium">Origin:</span> ${certificate.country_of_origin || 'N/A'}</div>
                            <div><span class="font-medium">Destination:</span> ${certificate.country_of_place_of_destination || 'N/A'}</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Consignor Information</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Name:</span> ${certificate.consignor_name || 'N/A'}</div>
                            <div><span class="font-medium">Country:</span> ${certificate.country_of_consignor || 'N/A'}</div>
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="font-semibold text-gray-900 mb-3">Consignee Information</h4>
                        <div class="space-y-2">
                            <div><span class="font-medium">Name:</span> ${certificate.consignee_name || 'N/A'}</div>
                            <div><span class="font-medium">Country:</span> ${certificate.country_of_consignee || 'N/A'}</div>
                        </div>
                    </div>
                    
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">Timestamps</h4>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div><span class="font-medium">Created:</span> ${certificate.create_date_time || 'N/A'}</div>
                            <div><span class="font-medium">Updated:</span> ${certificate.update_date_time || 'N/A'}</div>
                            <div><span class="font-medium">Status Changed:</span> ${certificate.status_change_date_time || 'N/A'}</div>
                            <div><span class="font-medium">Declaration:</span> ${certificate.declaration_date_time || 'N/A'}</div>
                        </div>
                    </div>
                    
                    ${certificate.commodities ? `
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">Commodities</h4>
                        <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto">${JSON.stringify(certificate.commodities, null, 2)}</pre>
                    </div>
                    ` : ''}
                    
                    ${certificate.references ? `
                    <div class="md:col-span-2">
                        <h4 class="font-semibold text-gray-900 mb-3">References</h4>
                        <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto">${JSON.stringify(certificate.references, null, 2)}</pre>
                    </div>
                    ` : ''}
                </div>
            `;
            
            document.getElementById('certificateDetails').innerHTML = detailsHtml;
            document.getElementById('certificateModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error fetching certificate details:', error);
            alert('Error loading certificate details');
        });
}

function closeCertificateModal() {
    document.getElementById('certificateModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('certificateModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCertificateModal();
    }
});
</script>
@endpush 
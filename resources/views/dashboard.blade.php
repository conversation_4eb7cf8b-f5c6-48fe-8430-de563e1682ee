@extends('layouts.app')

@section('title', 'Tableau de Bord')

@section('content')
<div class="space-y-6">
    <!-- Cartes de Statistiques -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-primary-500 to-blue-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-certificate text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Certificats Totals</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($totalCertificates) }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Certificats en Attente</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($pendingCertificates) }}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Certificats Envoyés</p>
                    <p class="text-2xl font-bold text-gray-900">{{ number_format($totalCertificates - $pendingCertificates) }}</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions Rapides -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="{{ route('certificates') }}" class="flex items-center p-4 bg-gradient-to-r from-primary-50 to-blue-50 border border-primary-200 rounded-lg hover:from-primary-100 hover:to-blue-100 transition-colors">
                <div class="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-download text-white"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-medium text-gray-900">Récupérer les Nouveaux Certificats</h4>
                    <p class="text-sm text-gray-600">Récupérer les derniers certificats depuis l'API TRACES</p>
                </div>
            </a>
            
            <a href="{{ route('api-parameters') }}" class="flex items-center p-4 bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-lg hover:from-purple-100 hover:to-indigo-100 transition-colors">
                <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                    <i class="fas fa-cog text-white"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-medium text-gray-900">Configuration de l'API</h4>
                    <p class="text-sm text-gray-600">Gérer les paramètres de l'API TRACES</p>
                </div>
            </a>
        </div>
    </div>
    
    <!-- Certificats Récents -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Certificats Récents</h3>
                <a href="{{ route('certificates') }}" class="text-primary-600 hover:text-primary-700 font-medium text-sm">
                    Voir Tout
                </a>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            ID du Certificat
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Stocké dans notre système
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Expéditeur
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Créé le
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($recentCertificates as $certificate)
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ $certificate->certificate_id }}</div>
                            <div class="text-sm text-gray-500">{{ $certificate->local_reference }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($certificate->status == 'sent')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check-circle mr-1"></i>
                                    Envoyé
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <i class="fas fa-clock mr-1"></i>
                                    En Attente
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900">{{ $certificate->consignor_name }}</div>
                            <div class="text-sm text-gray-500">{{ $certificate->country_of_consignor }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $certificate->created_at->format('d M Y H:i') }}
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center text-gray-500">
                            Aucun certificat trouvé. <a href="{{ route('certificates') }}" class="text-primary-600 hover:text-primary-700">Récupérez des certificats</a> pour commencer.
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

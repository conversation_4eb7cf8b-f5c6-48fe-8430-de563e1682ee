@extends('layouts.app')

@section('title', 'Détails de l\'Utilisateur')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{{ route('users.index') }}" class="text-gray-600 hover:text-gray-900">
                <i class="fas fa-arrow-left text-xl"></i>
            </a>
            <div>
                <h1 class="text-2xl font-bold text-gray-900">{{ $user->name }}</h1>
                <p class="text-gray-600">Détails de l'utilisateur</p>
            </div>
        </div>
        <div class="flex space-x-2">
            <a href="{{ route('users.edit', $user) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                <i class="fas fa-edit mr-2"></i>
                Modifier
            </a>
            @if(!$user->is_admin || \App\Models\User::where('is_admin', true)->count() > 1)
                <form method="POST" action="{{ route('users.destroy', $user) }}" class="inline" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                        <i class="fas fa-trash mr-2"></i>
                        Supprimer
                    </button>
                </form>
            @endif
        </div>
    </div>

    <!-- User Information -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Informations de l'Utilisateur</h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Profile Picture -->
                <div class="md:col-span-2 flex items-center space-x-4">
                    <div class="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-primary-600 text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-xl font-semibold text-gray-900">{{ $user->name }}</h2>
                        @if($user->is_admin)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <i class="fas fa-crown mr-1"></i>
                                Administrateur
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                <i class="fas fa-user mr-1"></i>
                                Utilisateur
                            </span>
                        @endif
                    </div>
                </div>

                <!-- Basic Information -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nom Complet</label>
                    <p class="text-gray-900">{{ $user->name }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nom d'Utilisateur</label>
                    <p class="text-gray-900">{{ $user->username }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Adresse Email</label>
                    <p class="text-gray-900">{{ $user->email }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Email Vérifié</label>
                    @if($user->email_verified_at)
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check mr-1"></i>
                            Vérifié le {{ $user->email_verified_at->format('d/m/Y H:i') }}
                        </span>
                    @else
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            Non vérifié
                        </span>
                    @endif
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Compte Créé</label>
                    <p class="text-gray-900">{{ $user->created_at->format('d/m/Y H:i') }}</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Dernière Modification</label>
                    <p class="text-gray-900">{{ $user->updated_at->format('d/m/Y H:i') }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Summary -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Résumé d'Activité</h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-certificate text-blue-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">
                        {{ \App\Models\Certificate::where('validated_by', $user->id)->count() }}
                    </p>
                    <p class="text-sm text-gray-600">Certificats Validés</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-calendar text-green-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">
                        {{ $user->created_at->diffInDays(now()) }}
                    </p>
                    <p class="text-sm text-gray-600">Jours depuis la Création</p>
                </div>
                
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-clock text-purple-600 text-xl"></i>
                    </div>
                    <p class="text-2xl font-bold text-gray-900">
                        {{ $user->updated_at->diffInDays(now()) }}
                    </p>
                    <p class="text-sm text-gray-600">Jours depuis la Dernière Modification</p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ONISPA Mauritanie - API TRACES</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    @stack('styles')
</head>
<body class="bg-gray-50">
    <div class="flex h-screen">
        <!-- <PERSON><PERSON> la<PERSON> -->
        <div class="w-64 bg-gradient-to-b from-primary-800 to-primary-900 text-white shadow-lg">
            <div class="p-6">
                <div class="flex items-center space-x-3">
                    <div class="w-16 h-16 bg-white rounded-lg flex items-center justify-center p-1">
                        <!-- <i class="fas fa-fish text-primary-600 text-xl"></i> -->
                        <img src="{{ asset('logo_onispa.png') }}" alt="ONISPA Logo" class="w-full h-full object-contain">
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">ONISPA</h1>
                        <p class="text-primary-200 text-sm">Mauritanie</p>
                    </div>
                </div>
            </div>
            
            <nav class="mt-8">
                <div class="px-4 space-y-2">
                    <a href="{{ route('dashboard') }}" class="flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors {{ request()->routeIs('dashboard') ? 'bg-primary-700 text-white' : '' }}">
                        <i class="fas fa-tachometer-alt w-5 h-5 mr-3"></i>
                        <span>Tableau de bord</span>
                    </a>
                    
                    <a href="{{ route('certificates') }}" class="flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors {{ request()->routeIs('certificates*') ? 'bg-primary-700 text-white' : '' }}">
                        <i class="fas fa-certificate w-5 h-5 mr-3"></i>
                        <span>Certificats</span>
                    </a>
                    
                    <a href="{{ route('api-parameters') }}" class="flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors {{ request()->routeIs('api-parameters*') ? 'bg-primary-700 text-white' : '' }}">
                        <i class="fas fa-cog w-5 h-5 mr-3"></i>
                        <span>Paramètres API</span>
                    </a>
                    
                    <a href="{{ route('logs') }}" class="flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors {{ request()->routeIs('logs*') ? 'bg-primary-700 text-white' : '' }}">
                        <i class="fas fa-list-alt w-5 h-5 mr-3"></i>
                        <span>Journaux</span>
                    </a>

                    @auth
                        @if(Auth::user()->isAdmin())
                            <a href="{{ route('users.index') }}" class="flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors {{ request()->routeIs('users*') ? 'bg-primary-700 text-white' : '' }}">
                                <i class="fas fa-users w-5 h-5 mr-3"></i>
                                <span>Utilisateurs</span>
                            </a>
                        @endif
                    @endauth
                </div>
            </nav>
            
            <div class="absolute bottom-0 w-64 p-4">
                <form method="POST" action="{{ route('logout') }}">
                    @csrf
                    <button type="submit" class="w-full flex items-center px-4 py-3 text-primary-100 hover:bg-primary-700 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt w-5 h-5 mr-3"></i>
                        <span>Déconnexion</span>
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Contenu principal -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- En-tête -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="px-6 py-4">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-semibold text-gray-800">@yield('title', 'Tableau de bord')</h2>
                        <div class="flex items-center space-x-4">
                            <span class="text-sm text-gray-600">Bienvenue, {{ session('user.name', 'Admin') }}</span>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Contenu de la page -->
            <main class="flex-1 overflow-y-auto p-6">
                @if(session('success'))
                    <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                        {{ session('success') }}
                    </div>
                @endif
                
                @if(session('error'))
                    <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        {{ session('error') }}
                    </div>
                @endif
                
                @yield('content')
            </main>
        </div>
    </div>
    
    @stack('scripts')
</body>
</html>
